/** https://fetch.spec.whatwg.org/#port-blocking */
export declare const KNOWN_RESERVED_PORTS: {
    readonly 1: "tcpmux";
    readonly 7: "echo";
    readonly 9: "discard";
    readonly 11: "systat";
    readonly 13: "daytime";
    readonly 15: "netstat";
    readonly 17: "qotd";
    readonly 19: "chargen";
    readonly 20: "ftp-data";
    readonly 21: "ftp";
    readonly 22: "ssh";
    readonly 23: "telnet";
    readonly 25: "smtp";
    readonly 37: "time";
    readonly 42: "name";
    readonly 43: "nicname";
    readonly 53: "domain";
    readonly 69: "tftp";
    readonly 77: "rje";
    readonly 79: "finger";
    readonly 87: "link";
    readonly 95: "supdup";
    readonly 101: "hostname";
    readonly 102: "iso-tsap";
    readonly 103: "gppitnp";
    readonly 104: "acr-nema";
    readonly 109: "pop2";
    readonly 110: "pop3";
    readonly 111: "sunrpc";
    readonly 113: "auth";
    readonly 115: "sftp";
    readonly 117: "uucp-path";
    readonly 119: "nntp";
    readonly 123: "ntp";
    readonly 135: "epmap";
    readonly 137: "netbios-ns";
    readonly 139: "netbios-ssn";
    readonly 143: "imap";
    readonly 161: "snmp";
    readonly 179: "bgp";
    readonly 389: "ldap";
    readonly 427: "svrloc";
    readonly 465: "submissions";
    readonly 512: "exec";
    readonly 513: "login";
    readonly 514: "shell";
    readonly 515: "printer";
    readonly 526: "tempo";
    readonly 530: "courier";
    readonly 531: "chat";
    readonly 532: "netnews";
    readonly 540: "uucp";
    readonly 548: "afp";
    readonly 554: "rtsp";
    readonly 556: "remotefs";
    readonly 563: "nntps";
    readonly 587: "submission";
    readonly 601: "syslog-conn";
    readonly 636: "ldaps";
    readonly 989: "ftps-data";
    readonly 990: "ftps";
    readonly 993: "imaps";
    readonly 995: "pop3s";
    readonly 1719: "h323gatestat";
    readonly 1720: "h323hostcall";
    readonly 1723: "pptp";
    readonly 2049: "nfs";
    readonly 3659: "apple-sasl";
    readonly 4045: "npp";
    readonly 5060: "sip";
    readonly 5061: "sips";
    readonly 6000: "x11";
    readonly 6566: "sane-port";
    readonly 6665: "ircu";
    readonly 6666: "ircu";
    readonly 6667: "ircu";
    readonly 6668: "ircu";
    readonly 6669: "ircu";
    readonly 6697: "ircs-u";
    readonly 10080: "amanda";
};
type ReservedPort = keyof typeof KNOWN_RESERVED_PORTS;
export declare function isPortIsReserved(port: number): port is ReservedPort;
export declare function getReservedPortExplanation(port: ReservedPort): string;
export {};
