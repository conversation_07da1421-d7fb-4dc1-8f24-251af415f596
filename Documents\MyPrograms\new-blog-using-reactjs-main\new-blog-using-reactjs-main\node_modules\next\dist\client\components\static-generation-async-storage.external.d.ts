/// <reference types="node" />
import type { AsyncLocalStorage } from 'async_hooks';
import type { IncrementalCache } from '../../server/lib/incremental-cache';
import type { DynamicServerError } from './hooks-server-context';
import type { FetchMetrics } from '../../server/base-http';
import type { Revalidate } from '../../server/lib/revalidate';
export interface StaticGenerationStore {
    readonly isStaticGeneration: boolean;
    readonly pagePath?: string;
    readonly urlPathname: string;
    readonly incrementalCache?: IncrementalCache;
    readonly isOnDemandRevalidate?: boolean;
    readonly isPrerendering?: boolean;
    readonly isRevalidate?: boolean;
    readonly isUnstableCacheCallback?: boolean;
    /**
     * If defined, this function when called will throw an error postponing
     * rendering during the React render phase. This should not be invoked outside
     * of the React render phase as it'll throw an error.
     */
    readonly postpone: ((reason: string) => never) | undefined;
    forceDynamic?: boolean;
    fetchCache?: 'only-cache' | 'force-cache' | 'default-cache' | 'force-no-store' | 'default-no-store' | 'only-no-store';
    revalidate?: Revalidate;
    forceStatic?: boolean;
    dynamicShouldError?: boolean;
    pendingRevalidates?: Record<string, Promise<any>>;
    postponeWasTriggered?: boolean;
    dynamicUsageDescription?: string;
    dynamicUsageStack?: string;
    dynamicUsageErr?: DynamicServerError;
    nextFetchId?: number;
    pathWasRevalidated?: boolean;
    tags?: string[];
    revalidatedTags?: string[];
    fetchMetrics?: FetchMetrics;
    isDraftMode?: boolean;
}
export type StaticGenerationAsyncStorage = AsyncLocalStorage<StaticGenerationStore>;
export declare const staticGenerationAsyncStorage: StaticGenerationAsyncStorage;
