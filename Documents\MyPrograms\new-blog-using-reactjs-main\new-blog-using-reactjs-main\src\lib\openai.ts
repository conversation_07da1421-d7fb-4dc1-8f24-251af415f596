import OpenAI from 'openai'

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

export interface AIModelConfig {
  deepSearch: string
  outlineGeneration: string
  contentGeneration: string
  imagePrompt: string
  imageGeneration: string
}

export const DEFAULT_AI_MODELS: AIModelConfig = {
  deepSearch: 'gpt-4o-mini',
  outlineGeneration: 'gpt-4o',
  contentGeneration: 'gpt-4o',
  imagePrompt: 'gpt-4o-mini',
  imageGeneration: 'dall-e-3'
}

export interface BlogOutline {
  title: string
  excerpt: string
  tags: string[]
  categories: string[]
  chapters: {
    title: string
    description: string
    keyPoints: string[]
  }[]
}

export interface ExternalSource {
  title: string
  url: string
  description: string
  type: 'study' | 'data' | 'survey' | 'academic' | 'blog' | 'article'
}

export interface InternalLink {
  title: string
  url: string
  relevance: string
}

// Deep search for external sources
export async function searchExternalSources(
  keyword: string,
  model: string = DEFAULT_AI_MODELS.deepSearch
): Promise<ExternalSource[]> {
  try {
    const prompt = `You are a research assistant. Find 5-7 high-quality external sources related to "${keyword}" that would be valuable for external linking in a blog post. 

Focus on:
- Academic studies and research papers
- Data and statistics from reputable sources
- Surveys from known institutions
- Articles from established publications
- Popular blogs from recognized experts

For each source, provide:
1. A descriptive title
2. The URL (use realistic URLs from known domains like .edu, .org, major publications)
3. A brief description of why it's relevant
4. The type of source

Return the response as a JSON array with this structure:
[
  {
    "title": "Source Title",
    "url": "https://example.com/source",
    "description": "Brief description of relevance",
    "type": "study|data|survey|academic|blog|article"
  }
]`

    const response = await openai.chat.completions.create({
      model,
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.7,
    })

    const content = response.choices[0]?.message?.content
    if (!content) throw new Error('No response from OpenAI')

    return JSON.parse(content)
  } catch (error) {
    console.error('Error searching external sources:', error)
    throw error
  }
}

// Generate blog outline and metadata
export async function generateBlogOutline(
  keyword: string,
  externalSources: ExternalSource[],
  internalLinks: InternalLink[],
  model: string = DEFAULT_AI_MODELS.outlineGeneration
): Promise<BlogOutline> {
  try {
    const sourcesText = externalSources.map(s => `- ${s.title}: ${s.description}`).join('\n')
    const internalLinksText = internalLinks.map(l => `- ${l.title}: ${l.relevance}`).join('\n')

    const prompt = `You are a content strategist writing in Seth Godin's style. Create a blog outline for the keyword "${keyword}".

Seth Godin's writing style characteristics:
- Short, punchy sentences
- Conversational tone
- Thought-provoking insights
- Stories and analogies
- Challenges conventional thinking
- Focuses on human behavior and marketing psychology
- Uses simple language but profound concepts

RULES:
- NO em dashes (—) anywhere
- NO colons in the title
- Title should be short but not too short (4-8 words)
- Create exactly 3 chapters
- Each chapter should be substantial enough for 500-800 words

Available external sources for linking:
${sourcesText}

Available internal links:
${internalLinksText}

Return a JSON object with this structure:
{
  "title": "Blog post title (no colons, 4-8 words)",
  "excerpt": "Compelling excerpt in Seth Godin style (2-3 sentences)",
  "tags": ["tag1", "tag2", "tag3", "tag4", "tag5"],
  "categories": ["category1", "category2"],
  "chapters": [
    {
      "title": "Chapter 1 Title",
      "description": "What this chapter covers",
      "keyPoints": ["point1", "point2", "point3"]
    },
    {
      "title": "Chapter 2 Title", 
      "description": "What this chapter covers",
      "keyPoints": ["point1", "point2", "point3"]
    },
    {
      "title": "Chapter 3 Title",
      "description": "What this chapter covers", 
      "keyPoints": ["point1", "point2", "point3"]
    }
  ]
}`

    const response = await openai.chat.completions.create({
      model,
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.8,
    })

    const content = response.choices[0]?.message?.content
    if (!content) throw new Error('No response from OpenAI')

    return JSON.parse(content)
  } catch (error) {
    console.error('Error generating blog outline:', error)
    throw error
  }
}

export default openai
