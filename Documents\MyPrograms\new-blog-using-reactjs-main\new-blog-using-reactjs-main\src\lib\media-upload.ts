// Media upload utilities for Firebase Storage

export interface UploadResult {
  success: boolean
  downloadUrl: string
  filename: string
  originalName?: string
  originalUrl?: string
  size: number
  contentType: string
  convertedToWebP: boolean
}

// Upload image from URL (for OpenAI generated images)
export async function uploadImageFromUrl(
  imageUrl: string,
  filename?: string,
  convertToWebP: boolean = true
): Promise<UploadResult> {
  try {
    const baseUrl = typeof window !== 'undefined'
      ? window.location.origin
      : process.env.NEXTAUTH_URL || 'http://localhost:3000'

    const response = await fetch(`${baseUrl}/api/media/upload`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        imageUrl,
        filename,
        convertToWebP
      })
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
    }

    const result = await response.json()
    return result

  } catch (error) {
    console.error('Error uploading image from URL:', error)
    throw error
  }
}

// Upload file from FormData (for manual uploads)
export async function uploadFile(
  file: File,
  filename?: string,
  convertToWebP: boolean = true
): Promise<UploadResult> {
  try {
    const formData = new FormData()
    formData.append('file', file)
    if (filename) formData.append('filename', filename)
    formData.append('convertToWebP', convertToWebP.toString())

    const baseUrl = typeof window !== 'undefined'
      ? window.location.origin
      : process.env.NEXTAUTH_URL || 'http://localhost:3000'

    const response = await fetch(`${baseUrl}/api/media/upload`, {
      method: 'POST',
      body: formData
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
    }

    const result = await response.json()
    return result

  } catch (error) {
    console.error('Error uploading file:', error)
    throw error
  }
}

// Generate filename from title
export function generateImageFilename(title: string, extension: string = 'webp'): string {
  const cleanTitle = title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
    .substring(0, 50) // Limit length

  return `${cleanTitle}.${extension}`
}

// Validate image URL
export function isValidImageUrl(url: string): boolean {
  try {
    const urlObj = new URL(url)
    return urlObj.protocol === 'http:' || urlObj.protocol === 'https:'
  } catch {
    return false
  }
}

// Get image info without downloading
export async function getImageInfo(imageUrl: string): Promise<{
  contentType: string
  contentLength: number
  isValid: boolean
}> {
  try {
    const response = await fetch(imageUrl, { method: 'HEAD' })
    
    return {
      contentType: response.headers.get('content-type') || 'unknown',
      contentLength: parseInt(response.headers.get('content-length') || '0'),
      isValid: response.ok && (response.headers.get('content-type')?.startsWith('image/') || false)
    }
  } catch (error) {
    console.error('Error getting image info:', error)
    return {
      contentType: 'unknown',
      contentLength: 0,
      isValid: false
    }
  }
}
