# AI Blog Generator

A comprehensive AI-powered blog generation system that creates high-quality blog posts in <PERSON>'s writing style with external research, internal linking, and featured image generation.

## Features

### ✨ Core Functionality
- **Keyword-based Generation**: Enter a keyword and generate a complete blog post
- **<PERSON> Style**: All content follows <PERSON>'s distinctive writing principles
- **External Research**: Automatically finds and includes relevant external sources
- **Internal Linking**: Suggests relevant internal blog posts for linking
- **Featured Images**: Generates pastel-colored, minimalist featured images using DALL-E
- **Content Validation**: Ensures all content follows style guidelines

### 🎯 Content Rules
- ❌ No em dashes (—) anywhere
- ❌ No colons (:) in titles
- ✅ Short, punchy sentences
- ✅ Conversational tone
- ✅ Thought-provoking insights
- ✅ 4-8 word titles
- ✅ 2-3 sentence excerpts

### 🤖 AI Model Selection
Choose different AI models for each generation step:
- **Deep Search**: Find external sources (GPT-4o, GPT-4o-mini, GPT-3.5-turbo)
- **Outline Generation**: Create blog structure (GPT-4o, GPT-4o-mini, GPT-3.5-turbo)
- **Content Generation**: Write chapters (GPT-4o, GPT-4o-mini, GPT-3.5-turbo)
- **Image Prompt**: Generate image descriptions (GPT-4o, GPT-4o-mini, GPT-3.5-turbo)
- **Image Generation**: Create featured images (DALL-E 3, DALL-E 2)

## How It Works

### 1. Keyword Input
Enter your target keyword or topic for the blog post.

### 2. Deep Search (20% progress)
- Searches for 6-8 high-quality external sources
- Includes academic studies, government data, surveys, industry reports
- Validates source credibility and relevance

### 3. Internal Link Discovery (30% progress)
- Analyzes existing blog posts in your sitemap
- Uses AI to find 3-5 most relevant internal posts to link to
- Explains relevance for each suggested link

### 4. Outline Generation (40% progress)
- Creates compelling title following Seth Godin style
- Generates engaging excerpt
- Suggests relevant tags and categories
- Plans exactly 3 substantial chapters

### 5. Content Generation (50-80% progress)
- Writes all 3 chapters with full context awareness
- Each chapter is 500-800 words
- Randomly includes tables, quotes, or code blocks
- Incorporates external and internal links naturally

### 6. Featured Image Generation (80-90% progress)
- Generates AI prompt for pastel, minimalist image
- Creates image using DALL-E
- Uploads to Firebase Storage
- Uses blog title as filename

### 7. Blog System Integration (90-100% progress)
- Validates content against style rules
- Saves as draft in Firebase
- Includes all metadata and sources
- Provides editing and publishing links

## Generated Content Structure

Each generated blog post includes:

```markdown
## Chapter 1 Title
[500-800 words of content with potential table/quote/code block]

---

## Chapter 2 Title  
[500-800 words of content with potential table/quote/code block]

---

## Chapter 3 Title
[500-800 words of content with potential table/quote/code block]

---

## Sources and References

### External Sources
- [Source Title](URL) - Description

### Related Articles  
- [Internal Post Title](URL) - Relevance explanation

---

*This blog post was generated using AI assistance and follows Seth Godin's writing principles.*
```

## API Endpoints

- `POST /api/ai/search-sources` - Find external sources
- `POST /api/ai/internal-links` - Get internal link suggestions  
- `POST /api/ai/generate-outline` - Create blog outline
- `POST /api/ai/generate-content` - Generate chapter content
- `POST /api/ai/generate-image` - Create featured image
- `POST /api/ai/save-blog` - Save to blog system

## Content Validation

The system automatically validates and cleans content:

### Errors (Auto-fixed)
- Removes em dashes (—)
- Removes colons from titles
- Ensures proper structure

### Warnings (Suggestions)
- Title length recommendations
- Sentence length optimization
- Conversational tone suggestions
- Thought-provoking element checks

## Usage

1. Navigate to `/dashboard/ai-generator`
2. Enter your keyword
3. Select AI models for each step (optional)
4. Click "Generate Blog Post"
5. Monitor progress through 6 steps
6. Review generated content
7. Edit or publish the draft

## Requirements

- OpenAI API key with GPT-4 and DALL-E access
- Firebase project with Firestore and Storage
- Admin access to the dashboard

## Configuration

Set these environment variables:

```env
OPENAI_API_KEY=your_openai_api_key
NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id
```

## Seth Godin Style Guidelines

The AI follows these principles:

### Writing Style
- Short, punchy sentences that pack impact
- Conversational, direct tone
- Thought-provoking insights that challenge assumptions
- Stories and analogies for complex concepts
- Focus on human behavior and psychology
- Simple language with profound meaning

### Content Structure
- Hook readers with opening story/observation
- Build to insight or revelation
- Connect to broader human truths
- Provide actionable takeaways
- Leave readers thinking differently

### Formatting Rules
- No em dashes (—) anywhere
- No colons (:) in titles
- Titles: 4-8 words, engaging
- Excerpts: 2-3 sentences, compelling
- Chapters: 3 substantial sections

## Troubleshooting

### Common Issues
1. **API Errors**: Check OpenAI API key and credits
2. **Firebase Errors**: Verify Firebase configuration
3. **Generation Fails**: Try different AI models
4. **Image Upload Fails**: Check Firebase Storage rules

### Performance Tips
- Use GPT-4o-mini for faster, cheaper generation
- Use GPT-4o for highest quality content
- DALL-E 3 for best image quality
- DALL-E 2 for faster image generation

## Future Enhancements

- Batch generation for multiple keywords
- Custom style templates beyond Seth Godin
- SEO optimization suggestions
- Social media post generation
- Content calendar integration
- A/B testing for titles and excerpts
