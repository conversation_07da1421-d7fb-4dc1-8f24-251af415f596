'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/components/providers/AuthProvider'
import { AIModelConfig, ExternalSource, InternalLink, BlogOutline, GeneratedChapter } from '@/types'
import { DEFAULT_AI_MODELS, MODEL_OPTIONS, IMAGE_MODEL_OPTIONS } from '@/lib/ai-models'
import { tokenTracker } from '@/lib/token-tracker'


interface AIBlogGeneratorFormProps {
  isGenerating: boolean
  setIsGenerating: (generating: boolean) => void
  onSessionStart?: (sessionId: string) => void
}

export default function AIBlogGeneratorForm({
  isGenerating,
  setIsGenerating,
  onSessionStart
}: AIBlogGeneratorFormProps) {
  const { user } = useAuth()
  const router = useRouter()
  const [keyword, setKeyword] = useState('')
  const [aiModels, setAiModels] = useState<AIModelConfig>(DEFAULT_AI_MODELS)

  // Load resumable sessions on component mount
  useEffect(() => {
    const resumable = tokenTracker.getResumableSessions()
    setResumableSessions(resumable)
    if (resumable.length > 0) {
      setShowRecoveryOptions(true)
    }
  }, [])

  // Redirect to new post page with generated content
  const redirectToNewPost = (outline: any, chapters: any[], featuredImageUrl: string) => {
    // Combine chapters into content without horizontal lines
    const content = chapters.map((chapter) => {
      return `## ${chapter.title}\n\n${chapter.content}`
    }).join('\n\n')

    // Only add sources section if we have actual sources or links
    let sourcesSection = ''
    const hasExternalSources = externalSources.length > 0
    const hasInternalLinks = internalLinks.length > 0

    if (hasExternalSources || hasInternalLinks) {
      sourcesSection = '\n\n## Sources and References\n\n'

      if (hasExternalSources) {
        sourcesSection += '### External Sources\n'
        sourcesSection += externalSources.map((source) =>
          `- [${source.title}](${source.url}) - ${source.description}`
        ).join('\n') + '\n\n'
      }

      if (hasInternalLinks) {
        sourcesSection += '### Related Articles\n'
        sourcesSection += internalLinks.map((link) =>
          `- [${link.title}](${link.url}) - ${link.relevance}`
        ).join('\n')
      }
    }

    const fullContent = content + sourcesSection

    // Store data in sessionStorage to avoid URL length limits
    const generatedData = {
      title: outline.title,
      excerpt: outline.excerpt,
      content: fullContent,
      featured_image: featuredImageUrl,
      tags: (outline.tags || []).join(', '),
      categories: (outline.categories || []).join(', '),
      ai_generated: 'true'
    }

    // Store in sessionStorage (will be cleared when tab is closed)
    sessionStorage.setItem('ai_generated_blog_data', JSON.stringify(generatedData))

    // Redirect to new post page without parameters
    router.push('/dashboard/posts/new')
  }
  const [currentStep, setCurrentStep] = useState<'input' | 'searching' | 'outline' | 'content' | 'image' | 'complete'>('input')
  const [externalSources, setExternalSources] = useState<ExternalSource[]>([])
  const [internalLinks, setInternalLinks] = useState<InternalLink[]>([])
  const [outline, setOutline] = useState<BlogOutline | null>(null)
  const [chapters, setChapters] = useState<GeneratedChapter[]>([])
  const [featuredImageUrl, setFeaturedImageUrl] = useState<string>('')
  const [progress, setProgress] = useState(0)
  const [validationResults, setValidationResults] = useState<any>(null)
  const [generatedPostId, setGeneratedPostId] = useState<string>('')
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null)
  const [statusMessage, setStatusMessage] = useState<string>('')
  const [contentProgress, setContentProgress] = useState<{
    currentBatch: number;
    totalBatches: number;
    totalChapters: number;
  } | null>(null)
  const [resumableSessions, setResumableSessions] = useState<any[]>([])
  const [showRecoveryOptions, setShowRecoveryOptions] = useState(false)



  const handleModelChange = (field: keyof AIModelConfig, value: string) => {
    setAiModels(prev => ({ ...prev, [field]: value }))
  }

  const resetForm = () => {
    // End current session if exists
    if (currentSessionId) {
      tokenTracker.endSession(currentSessionId, 'failed')
    }

    setKeyword('')
    setCurrentStep('input')
    setExternalSources([])
    setInternalLinks([])
    setOutline(null)
    setChapters([])
    setFeaturedImageUrl('')
    setProgress(0)
    setValidationResults(null)
    setGeneratedPostId('')
    setCurrentSessionId(null)
    setStatusMessage('')
    setContentProgress(null)
  }

  const resumeFromCache = async (cachedSessionId: string) => {
    const cachedData = tokenTracker.getCachedSessionData(cachedSessionId)
    if (!cachedData) return

    // Resume session
    const sessionId = tokenTracker.resumeSession(cachedSessionId)
    setCurrentSessionId(sessionId)
    onSessionStart?.(sessionId)

    // Restore cached data
    if (cachedData.externalSources) setExternalSources(cachedData.externalSources)
    if (cachedData.internalLinks) setInternalLinks(cachedData.internalLinks)
    if (cachedData.outline) setOutline(cachedData.outline)
    if (cachedData.chapters) setChapters(cachedData.chapters)
    if (cachedData.featuredImageUrl) setFeaturedImageUrl(cachedData.featuredImageUrl)
    if (cachedData.aiModels) setAiModels(cachedData.aiModels)

    // Continue from where we left off
    const lastStep = cachedData.lastCompletedStep
    setStatusMessage(`Resuming from ${lastStep} step...`)

    if (lastStep === 'searching') {
      setCurrentStep('outline')
      setProgress(40)
      await continueGeneration(sessionId, cachedData)
    } else if (lastStep === 'outline') {
      setCurrentStep('content')
      setProgress(50)
      await continueGeneration(sessionId, cachedData)
    } else if (lastStep === 'content') {
      setCurrentStep('image')
      setProgress(85)
      await continueGeneration(sessionId, cachedData)
    } else if (lastStep === 'image') {
      setCurrentStep('complete')
      setProgress(100)
      await continueGeneration(sessionId, cachedData)
    }
  }

  const continueGeneration = async (sessionId: string, cachedData: any) => {
    setIsGenerating(true)

    try {
      const lastStep = cachedData.lastCompletedStep

      if (lastStep === 'searching' && cachedData.externalSources && cachedData.internalLinks) {
        // Continue with outline generation
        setStatusMessage('Generating blog outline...')
        const blogOutline = await generateOutline(keyword, cachedData.externalSources, cachedData.internalLinks, aiModels.outlineGeneration, sessionId)
        setOutline(blogOutline)

        // Cache the outline
        tokenTracker.cacheSessionData(sessionId, 'outline', {
          ...cachedData,
          outline: blogOutline,
          aiModels
        })

        // Continue to content generation
        await continueContentGeneration(sessionId, blogOutline, cachedData.externalSources, cachedData.internalLinks)
      } else if (lastStep === 'outline' && cachedData.outline) {
        // Continue with content generation
        await continueContentGeneration(sessionId, cachedData.outline, cachedData.externalSources, cachedData.internalLinks)
      } else if (lastStep === 'content' && cachedData.chapters) {
        // Continue with image generation
        await continueImageGeneration(sessionId, cachedData.outline)
      } else if (lastStep === 'image') {
        // Continue with saving
        await continueSaving(sessionId, cachedData)
      }
    } catch (error) {
      console.error('Error continuing generation:', error)
      tokenTracker.endSession(sessionId, 'failed')
      alert(`Error continuing generation: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setIsGenerating(false)
    }
  }

  const continueContentGeneration = async (sessionId: string, outline: any, sources: any[], internal: any[]) => {
    setCurrentStep('content')
    setProgress(50)
    const totalChapters = outline.chapters.length
    const totalBatches = Math.ceil(totalChapters / 3)
    setContentProgress({ currentBatch: 0, totalBatches, totalChapters })
    setStatusMessage(`Creating content chapters (${totalChapters} chapters in ${totalBatches} batches)...`)

    const generatedChapters = await generateChapters(
      outline,
      sources,
      internal,
      aiModels.contentGeneration,
      sessionId,
      (batchNumber, totalBatches, currentChapters) => {
        setContentProgress({ currentBatch: batchNumber, totalBatches, totalChapters })
        setStatusMessage(`Generated batch ${batchNumber}/${totalBatches} (${currentChapters.length}/${totalChapters} chapters complete)`)
        setChapters([...currentChapters])
        const contentProgress = (batchNumber / totalBatches) * 30
        setProgress(50 + contentProgress)

        // Cache progress after each batch
        tokenTracker.cacheSessionData(sessionId, 'content', {
          externalSources: sources,
          internalLinks: internal,
          outline,
          chapters: currentChapters,
          currentBatch: batchNumber,
          totalBatches,
          aiModels
        })
      }
    )
    setChapters(generatedChapters)

    // Cache completed content
    tokenTracker.cacheSessionData(sessionId, 'content', {
      externalSources: sources,
      internalLinks: internal,
      outline,
      chapters: generatedChapters,
      aiModels
    })

    await continueImageGeneration(sessionId, outline)
  }

  const continueImageGeneration = async (sessionId: string, outline: any) => {
    setCurrentStep('image')
    setProgress(85)
    setStatusMessage('Generating AI image with DALL-E...')

    let imageUrl = ''
    try {
      imageUrl = await generateFeaturedImage(outline.title, outline.excerpt, aiModels.imagePrompt, aiModels.imageGeneration, sessionId)
      setFeaturedImageUrl(imageUrl)
      setStatusMessage('Featured image generated successfully!')

      // Cache image
      tokenTracker.cacheSessionData(sessionId, 'image', { featuredImageUrl: imageUrl })
    } catch (imageError) {
      console.warn('Image generation failed, continuing without image:', imageError)
      setStatusMessage('Image generation failed, continuing without image...')
      setFeaturedImageUrl('')
    }

    await continueSaving(sessionId, { outline, chapters, featuredImageUrl: imageUrl })
  }

  const continueSaving = async (sessionId: string, data: any) => {
    setProgress(90)
    setStatusMessage('Preparing blog post...')

    // Instead of saving, redirect to the new post page with data
    redirectToNewPost(data.outline, data.chapters, data.featuredImageUrl || '')

    tokenTracker.endSession(sessionId, 'completed')
    setCurrentStep('complete')
    setProgress(100)
    setStatusMessage('Blog post ready! Redirecting to editor...')
  }

  const handleGenerate = async () => {
    if (!keyword.trim() || !user) return

    // Start new session
    const sessionId = tokenTracker.startSession(keyword.trim())
    setCurrentSessionId(sessionId)
    onSessionStart?.(sessionId)

    setIsGenerating(true)
    setCurrentStep('searching')
    setProgress(10)

    try {
      // Step 1: Deep Search for External Sources
      setProgress(20)
      setStatusMessage('Searching for external sources...')
      const sources = await searchExternalSources(keyword, aiModels.deepSearch, sessionId)
      setExternalSources(sources)

      // Step 2: Get Internal Links from Sitemap
      setProgress(30)
      setStatusMessage('Finding internal links...')
      const internal = await getInternalLinks(keyword)
      setInternalLinks(internal)

      // Cache search results
      tokenTracker.cacheSessionData(sessionId, 'searching', {
        externalSources: sources,
        internalLinks: internal,
        aiModels
      })

      // Step 3: Generate Outline and Metadata
      setCurrentStep('outline')
      setProgress(40)
      setStatusMessage('Generating blog outline...')
      const blogOutline = await generateOutline(keyword, sources, internal, aiModels.outlineGeneration, sessionId)
      setOutline(blogOutline)

      // Cache outline
      tokenTracker.cacheSessionData(sessionId, 'outline', {
        externalSources: sources,
        internalLinks: internal,
        outline: blogOutline,
        aiModels
      })

      // Step 4: Generate Content (in batches of 3 chapters)
      setCurrentStep('content')
      setProgress(50)
      const totalChapters = blogOutline.chapters.length
      const totalBatches = Math.ceil(totalChapters / 3)
      setContentProgress({ currentBatch: 0, totalBatches, totalChapters })
      setStatusMessage(`Creating content chapters (${totalChapters} chapters in ${totalBatches} batches)...`)

      const generatedChapters = await generateChapters(
        blogOutline,
        sources,
        internal,
        aiModels.contentGeneration,
        sessionId,
        (batchNumber, totalBatches, currentChapters) => {
          setContentProgress({ currentBatch: batchNumber, totalBatches, totalChapters })
          setStatusMessage(`Generated batch ${batchNumber}/${totalBatches} (${currentChapters.length}/${totalChapters} chapters complete)`)
          setChapters([...currentChapters]) // Update chapters as they're generated
          // Update progress: 50% base + 30% for content generation
          const contentProgress = (batchNumber / totalBatches) * 30
          setProgress(50 + contentProgress)
        }
      )
      setChapters(generatedChapters)

      // Step 5: Generate Featured Image (with fallback)
      setCurrentStep('image')
      setProgress(85)
      setStatusMessage('Generating AI image with DALL-E...')
      let imageUrl = ''
      try {
        imageUrl = await generateFeaturedImage(blogOutline.title, blogOutline.excerpt, aiModels.imagePrompt, aiModels.imageGeneration, sessionId)
        setFeaturedImageUrl(imageUrl)
        setStatusMessage('Featured image generated and uploaded successfully!')
      } catch (imageError) {
        console.warn('Image generation failed, continuing without image:', imageError)
        setStatusMessage('Image generation failed, continuing without image...')
        // Continue without image - this is acceptable since the post will be a draft
        setFeaturedImageUrl('')
      }

      // Step 6: Prepare and Redirect to Editor
      setProgress(90)
      setStatusMessage('Preparing blog post...')

      // Instead of saving, redirect to the new post page with all data
      redirectToNewPost(blogOutline, generatedChapters, imageUrl)

      // End session successfully
      tokenTracker.endSession(sessionId, 'completed')
      setCurrentStep('complete')
      setProgress(100)
      setStatusMessage('Blog post generated successfully!')
    } catch (error) {
      console.error('Error generating blog:', error)

      // Cache whatever data we have so far to prevent token waste
      if (sessionId) {
        const cacheData: any = { aiModels }
        if (externalSources.length > 0) cacheData.externalSources = externalSources
        if (internalLinks.length > 0) cacheData.internalLinks = internalLinks
        if (outline) cacheData.outline = outline
        if (chapters.length > 0) cacheData.chapters = chapters
        if (featuredImageUrl) cacheData.featuredImageUrl = featuredImageUrl

        // Determine last completed step
        let lastStep: any = 'searching'
        if (chapters.length > 0) lastStep = 'content'
        else if (outline) lastStep = 'outline'
        else if (externalSources.length > 0 && internalLinks.length > 0) lastStep = 'searching'

        tokenTracker.cacheSessionData(sessionId, lastStep, cacheData)
        tokenTracker.endSession(sessionId, 'failed')

        // Update resumable sessions
        const resumable = tokenTracker.getResumableSessions()
        setResumableSessions(resumable)
        if (resumable.length > 0) {
          setShowRecoveryOptions(true)
        }
      }

      alert(`Error generating blog: ${error instanceof Error ? error.message : 'Unknown error'}. Your progress has been saved and you can resume from where it failed.`)
    } finally {
      setIsGenerating(false)
    }
  }

  // API functions with retry logic
  const searchExternalSources = async (keyword: string, model: string, sessionId: string): Promise<ExternalSource[]> => {
    const maxRetries = 3
    let lastError: Error | null = null

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const response = await fetch('/api/ai/search-sources', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ keyword, model, sessionId })
        })

        if (!response.ok) throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        const data = await response.json()
        return data.sources
      } catch (error) {
        lastError = error as Error
        console.warn(`Search sources attempt ${attempt}/${maxRetries} failed:`, error)

        if (attempt < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt))
        }
      }
    }

    throw new Error(`Failed to search external sources after ${maxRetries} attempts: ${lastError?.message}`)
  }

  const getInternalLinks = async (keyword: string): Promise<InternalLink[]> => {
    const response = await fetch('/api/ai/internal-links', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ keyword })
    })

    if (!response.ok) throw new Error('Failed to get internal links')
    const data = await response.json()
    return data.internalLinks
  }

  const generateOutline = async (keyword: string, sources: ExternalSource[], internal: InternalLink[], model: string, sessionId: string): Promise<BlogOutline> => {
    const maxRetries = 3
    let lastError: Error | null = null

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const response = await fetch('/api/ai/generate-outline', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ keyword, externalSources: sources, internalLinks: internal, model, sessionId })
        })

        if (!response.ok) throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        const data = await response.json()
        return data.outline
      } catch (error) {
        lastError = error as Error
        console.warn(`Generate outline attempt ${attempt}/${maxRetries} failed:`, error)

        if (attempt < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt))
        }
      }
    }

    throw new Error(`Failed to generate outline after ${maxRetries} attempts: ${lastError?.message}`)
  }

  const generateChapters = async (outline: BlogOutline, sources: ExternalSource[], internal: InternalLink[], model: string, sessionId: string): Promise<GeneratedChapter[]> => {
    const maxRetries = 3
    let lastError: Error | null = null

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const response = await fetch('/api/ai/generate-content', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ outline, externalSources: sources, internalLinks: internal, model, sessionId })
        })

        if (!response.ok) throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        const data = await response.json()
        return data.chapters
      } catch (error) {
        lastError = error as Error
        console.warn(`Generate chapters attempt ${attempt}/${maxRetries} failed:`, error)

        if (attempt < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt))
        }
      }
    }

    throw new Error(`Failed to generate chapters after ${maxRetries} attempts: ${lastError?.message}`)
  }

  const generateFeaturedImage = async (title: string, excerpt: string, promptModel: string, imageModel: string, sessionId: string): Promise<string> => {
    // Note: Retry logic is already handled in the API route
    const response = await fetch('/api/ai/generate-image', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ title, excerpt, promptModel, imageModel, sessionId })
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    return data.imageUrl
  }

  const saveToBlogSystem = async (outline: BlogOutline, chapters: GeneratedChapter[], imageUrl: string, sources: ExternalSource[], internal: InternalLink[], userId: string) => {
    const response = await fetch('/api/ai/save-blog', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        outline,
        chapters,
        featuredImageUrl: imageUrl,
        externalSources: sources,
        internalLinks: internal,
        userId
      })
    })

    if (!response.ok) throw new Error('Failed to save blog post')
    const data = await response.json()
    return data
  }

  return (
    <div className="dashboard-card rounded-lg shadow-lg p-6">
      {/* Recovery Options */}
      {showRecoveryOptions && resumableSessions.length > 0 && (
        <div className="mb-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200">
              💰 Recover Previous Sessions
            </h3>
            <button
              onClick={() => setShowRecoveryOptions(false)}
              className="text-yellow-600 hover:text-yellow-800 dark:text-yellow-400 dark:hover:text-yellow-200"
            >
              ✕
            </button>
          </div>
          <p className="text-sm text-yellow-700 dark:text-yellow-300 mb-4">
            Don't waste your tokens! You have {resumableSessions.length} session(s) that can be resumed from where they failed.
          </p>
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {resumableSessions.map((session) => (
              <div key={session.id} className="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded border">
                <div className="flex-1">
                  <div className="font-medium text-gray-900 dark:text-gray-100">
                    {session.keyword}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Last step: {session.cachedData?.lastCompletedStep} •
                    Cost so far: ${session.totalCost.toFixed(3)} •
                    {new Date(session.startTime).toLocaleDateString()}
                  </div>
                </div>
                <button
                  onClick={() => {
                    setKeyword(session.keyword)
                    resumeFromCache(session.id)
                    setShowRecoveryOptions(false)
                  }}
                  disabled={isGenerating}
                  className="ml-3 px-3 py-1 bg-green-600 hover:bg-green-700 text-white text-sm rounded transition-colors disabled:opacity-50"
                >
                  Resume
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Keyword Input Section */}
      <div className="mb-8">
        <label htmlFor="keyword" className="block text-sm font-medium dashboard-text mb-2">
          Keyword
        </label>
        <input
          type="text"
          id="keyword"
          value={keyword}
          onChange={(e) => setKeyword(e.target.value)}
          placeholder="Enter your target keyword..."
          className="w-full px-4 py-3 dashboard-border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dashboard-input"
          disabled={isGenerating}
        />
      </div>

      {/* AI Model Selection */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <div>
          <label className="block text-sm font-medium dashboard-text mb-2">
            Deep Search Model
          </label>
          <select
            value={aiModels.deepSearch}
            onChange={(e) => handleModelChange('deepSearch', e.target.value)}
            className="w-full px-3 py-2 dashboard-border rounded-lg dashboard-input select-xs"
            disabled={isGenerating}
          >
            {MODEL_OPTIONS.map(option => (
              <option key={option.value} value={option.value}>{option.label}</option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium dashboard-text mb-2">
            Outline Generation Model
          </label>
          <select
            value={aiModels.outlineGeneration}
            onChange={(e) => handleModelChange('outlineGeneration', e.target.value)}
            className="w-full px-3 py-2 dashboard-border rounded-lg dashboard-input select-xs"
            disabled={isGenerating}
          >
            {MODEL_OPTIONS.map(option => (
              <option key={option.value} value={option.value}>{option.label}</option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium dashboard-text mb-2">
            Content Generation Model
          </label>
          <select
            value={aiModels.contentGeneration}
            onChange={(e) => handleModelChange('contentGeneration', e.target.value)}
            className="w-full px-3 py-2 dashboard-border rounded-lg dashboard-input select-xs"
            disabled={isGenerating}
          >
            {MODEL_OPTIONS.map(option => (
              <option key={option.value} value={option.value}>{option.label}</option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium dashboard-text mb-2">
            Image Prompt Model
          </label>
          <select
            value={aiModels.imagePrompt}
            onChange={(e) => handleModelChange('imagePrompt', e.target.value)}
            className="w-full px-3 py-2 dashboard-border rounded-lg dashboard-input select-xs"
            disabled={isGenerating}
          >
            {MODEL_OPTIONS.map(option => (
              <option key={option.value} value={option.value}>{option.label}</option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium dashboard-text mb-2">
            Image Generation Model
          </label>
          <select
            value={aiModels.imageGeneration}
            onChange={(e) => handleModelChange('imageGeneration', e.target.value)}
            className="w-full px-3 py-2 dashboard-border rounded-lg dashboard-input select-xs"
            disabled={isGenerating}
          >
            {IMAGE_MODEL_OPTIONS.map(option => (
              <option key={option.value} value={option.value}>{option.label}</option>
            ))}
          </select>
        </div>
      </div>

      {/* Generate Button */}
      <div className="mb-8">
        <div className="flex gap-4">
          <button
            onClick={handleGenerate}
            disabled={!keyword.trim() || isGenerating}
            className="flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-3 px-6 rounded-lg transition-colors"
          >
            {isGenerating ? 'Generating...' : 'Generate Blog Post'}
          </button>

          {(outline || currentStep !== 'input') && (
            <button
              onClick={resetForm}
              disabled={isGenerating}
              className="bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 text-white font-medium py-3 px-6 rounded-lg transition-colors"
            >
              Reset
            </button>
          )}
        </div>
      </div>

      {/* Progress Section */}
      {isGenerating && (
        <div className="mb-8">
          <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
            <span>Progress</span>
            <span>{progress}%</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
          <div className="mt-2 text-sm text-gray-600 dark:text-gray-400">
            {statusMessage || (
              <>
                {currentStep === 'searching' && 'Searching for external sources...'}
                {currentStep === 'outline' && 'Generating outline and metadata...'}
                {currentStep === 'content' && 'Creating content chapters...'}
                {currentStep === 'image' && 'Generating featured image...'}
                {currentStep === 'complete' && 'Blog post generated successfully!'}
              </>
            )}
            {contentProgress && currentStep === 'content' && (
              <div className="mt-2 text-xs text-blue-600 dark:text-blue-400">
                Batch {contentProgress.currentBatch}/{contentProgress.totalBatches} •
                {chapters.length}/{contentProgress.totalChapters} chapters complete
              </div>
            )}
          </div>
        </div>
      )}

      {/* Results Preview */}
      {outline && (
        <div className="border-t border-gray-200 dark:border-gray-700 pt-8">
          <h3 className="text-lg font-semibold text-black dark:text-white mb-4">Generated Content Preview</h3>

          <div className="space-y-6">
            {/* Basic Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-2">Title:</h4>
                <p className="text-black dark:text-white bg-gray-50 dark:bg-gray-800 p-3 rounded">{outline.title}</p>
              </div>

              <div>
                <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-2">Excerpt:</h4>
                <p className="text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 p-3 rounded">{outline.excerpt}</p>
              </div>
            </div>

            {/* Tags and Categories */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-2">Tags:</h4>
                <div className="flex flex-wrap gap-2">
                  {outline.tags.map((tag, index) => (
                    <span key={index} className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-sm">
                      {tag}
                    </span>
                  ))}
                </div>
              </div>

              <div>
                <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-2">Categories:</h4>
                <div className="flex flex-wrap gap-2">
                  {outline.categories.map((category, index) => (
                    <span key={index} className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded text-sm">
                      {category}
                    </span>
                  ))}
                </div>
              </div>
            </div>

            {/* Chapters */}
            {chapters.length > 0 && (
              <div>
                <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-2">Chapters:</h4>
                <div className="space-y-3">
                  {chapters.map((chapter, index) => (
                    <div key={index} className="bg-gray-50 dark:bg-gray-800 p-4 rounded">
                      <h5 className="font-medium text-black dark:text-white mb-2">
                        Chapter {index + 1}: {chapter.title}
                      </h5>
                      <div className="flex gap-2 mb-2">
                        {chapter.hasTable && (
                          <span className="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-2 py-1 rounded text-xs">
                            Table
                          </span>
                        )}
                        {chapter.hasQuote && (
                          <span className="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-2 py-1 rounded text-xs">
                            Quote
                          </span>
                        )}
                        {chapter.hasCodeBlock && (
                          <span className="bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-2 py-1 rounded text-xs">
                            Code Block
                          </span>
                        )}
                      </div>
                      <p className="text-gray-600 dark:text-gray-400 text-sm">
                        {chapter.content.substring(0, 200)}...
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Featured Image */}
            {featuredImageUrl && (
              <div>
                <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-2">Featured Image:</h4>
                <img
                  src={featuredImageUrl}
                  alt="Generated featured image"
                  className="w-full max-w-md rounded-lg shadow-md"
                />
              </div>
            )}

            {/* External Sources */}
            {externalSources.length > 0 && (
              <div>
                <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-2">External Sources ({externalSources.length}):</h4>
                <div className="space-y-2">
                  {externalSources.slice(0, 3).map((source, index) => (
                    <div key={index} className="bg-gray-50 dark:bg-gray-800 p-3 rounded">
                      <a href={source.url} target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:underline font-medium">
                        {source.title}
                      </a>
                      <p className="text-gray-600 dark:text-gray-400 text-sm mt-1">{source.description}</p>
                      <span className="inline-block bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-2 py-1 rounded text-xs mt-2">
                        {source.type}
                      </span>
                    </div>
                  ))}
                  {externalSources.length > 3 && (
                    <p className="text-gray-500 dark:text-gray-400 text-sm">
                      And {externalSources.length - 3} more sources...
                    </p>
                  )}
                </div>
              </div>
            )}

            {/* Internal Links */}
            {internalLinks.length > 0 && (
              <div>
                <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-2">Internal Links ({internalLinks.length}):</h4>
                <div className="space-y-2">
                  {internalLinks.map((link, index) => (
                    <div key={index} className="bg-gray-50 dark:bg-gray-800 p-3 rounded">
                      <a href={link.url} className="text-blue-600 dark:text-blue-400 hover:underline font-medium">
                        {link.title}
                      </a>
                      <p className="text-gray-600 dark:text-gray-400 text-sm mt-1">{link.relevance}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Validation Results */}
            {validationResults && (
              <div>
                <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-2">Content Validation:</h4>
                <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded">
                  {validationResults.errors.length === 0 && validationResults.warnings.length === 0 ? (
                    <div className="flex items-center text-green-600 dark:text-green-400">
                      <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      All content follows Seth Godin style guidelines!
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {validationResults.errors.length > 0 && (
                        <div>
                          <h5 className="font-medium text-red-600 dark:text-red-400 mb-2">Errors Fixed:</h5>
                          <ul className="list-disc list-inside text-red-600 dark:text-red-400 text-sm space-y-1">
                            {validationResults.errors.map((error: string, index: number) => (
                              <li key={index}>{error}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                      {validationResults.warnings.length > 0 && (
                        <div>
                          <h5 className="font-medium text-yellow-600 dark:text-yellow-400 mb-2">Style Suggestions:</h5>
                          <ul className="list-disc list-inside text-yellow-600 dark:text-yellow-400 text-sm space-y-1">
                            {validationResults.warnings.map((warning: string, index: number) => (
                              <li key={index}>{warning}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Success Message and Actions */}
            {currentStep === 'complete' && generatedPostId && (
              <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                <div className="flex items-center mb-3">
                  <svg className="w-6 h-6 text-green-600 dark:text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <h4 className="font-medium text-green-800 dark:text-green-200">Blog Post Generated Successfully!</h4>
                </div>
                <p className="text-green-700 dark:text-green-300 mb-4">
                  Your AI-generated blog post has been saved as a draft and is ready for review.
                </p>
                <div className="flex gap-3">
                  <a
                    href={`/dashboard/posts/${generatedPostId}`}
                    className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                  >
                    Edit Post
                  </a>
                  <a
                    href="/dashboard/posts"
                    className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                  >
                    View All Posts
                  </a>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
